import { ModelDefinition, Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import {Document, Types} from 'mongoose';

export type SyncPublisherDocument = SyncPublisher & Document;

@Schema({ versionKey: false, timestamps: { updatedAt: 'modifiedAt' } })
export class SyncPublisher {
	@Prop()
	_id: Types.ObjectId;

	@Prop()
	name: string;

	@Prop()
	publisherCd: string;

	@Prop()
	cmsType: string;
}

export const SyncPublisherSchema = SchemaFactory.createForClass(SyncPublisher);

export const SyncPublisherModel: ModelDefinition = {
	name: SyncPublisher.name,
	schema: SyncPublisherSchema,
	collection: 'SyncPublishers',
};
