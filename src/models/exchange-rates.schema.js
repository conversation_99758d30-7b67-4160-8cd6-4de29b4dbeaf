'use strict';

import mongoose from 'mongoose';

import _ from 'lodash';
import { BusinessError } from '../common/error';


let ExchangeRate = {};

let createSchemaAndModel = (conn) => {
	let scheme = mongoose.Schema({
		// 환율일자
		yyyymmdd: { type: String, trim: true, required:true },
		
		// 변환 통화코드
		currencyCdFrom: { type: String, trim: true, required:true },

		// 변환 대상 통화코드
		currencyCdTo: { type: String, trim: true, required:true },
		
		// 환율
		exchangeRate: { type: mongoose.Schema.Types.Decimal128, set: _setExchangeRate }, 

		createdAt: {
			type: Date,
			default: Date.now
		}
	});

	ExchangeRate = conn.model('ExchangeRate', scheme, 'ExchangeRates');

	return scheme;
};

const _setExchangeRate = (exchangeRate) => {
	if(_.isNil(exchangeRate) || _.isNaN(exchangeRate)) {
		throw new BusinessError({ code: '-1', message: `[exchange-rates.schema] exchangeRate validation 오류 : ${exchangeRate}` });
	}

	return exchangeRate;
};

export default createSchemaAndModel;

export { ExchangeRate };
