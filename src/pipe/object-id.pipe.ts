import { BadRequestException, Injectable, PipeTransform } from '@nestjs/common';
import { ArgumentMetadata } from '@nestjs/common/interfaces/features/pipe-transform.interface';

import _ from 'lodash';
import { isValidObjectId, Types } from 'mongoose';

import { CommonReportErrorCode } from '../error/error-code';
import { getReplacedErrorCode } from '../error/error-util';

/**
 * String -> ObjectId 타입 변환
 */
@Injectable()
export class ObjectIdPipe implements PipeTransform {
	constructor(
		// private readonly config?: { each: boolean }
	) {}

	async transform(value: string, metadata: ArgumentMetadata) {
		if (_.isEmpty(value)) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE, { property: metadata.data }, false)]);
		}

		if (!isValidObjectId(value)) {
			throw new BadRequestException([getReplacedErrorCode(CommonReportErrorCode.INVALID_VALUE, { property: metadata.data }, false)]);
		}

		return new Types.ObjectId(value);
	}
}
