'use strict';


import logger from '../utils/logger.util';

import * as dataController from '../temp/data.controller';

module.exports = router => {
	logger.info('[data.router] 호출됨');

	// 리얼 - sourcePath 스케쥴 설정 및 AP 원본 파일 HDFS 백업 (source=AP & OUTSIDE 대상)
	router.get('/onetime/backupSilvergreySource', dataController.backupSilvergreySource);

	// 로컬
	router.get('/onetime/compareCsvFiles', dataController.compareCsvFiles);
	router.get('/onetime/validateGfpFeeReprocess', dataController.validateGfpFeeReprocess);

	// AP 원본 파일 재처리
	router.get('/onetime/reprocessSilvergrey', dataController.reprocessSilvergrey);
};
