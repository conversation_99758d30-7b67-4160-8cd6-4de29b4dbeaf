'use strict';

import COMMON_CODE from "@ssp/ssp-common-code";
import child_process from "child_process";
import { createObjectCsvWriter } from "csv-writer";
import fs from "fs";
import _ from 'lodash';
import moment from 'moment';
import mongoose, { Types } from 'mongoose';
import path from "path";
import { BusinessError } from '../../common/error';
import config from "../../config/config";

import { AdProvider } from '../../models/ad-providers.schema';
import { DataEnvironments } from "../../models/data/data-environments.schema";
import { DiscrepancyDaily } from "../../models/data/discrepancy-daily.schema";
import { DiscrepancyReportSchedule } from '../../models/data/discrepancy-report-schedule.schema';
import { Publisher } from '../../models/publishers.schema';

import * as nclavis from "../../nclavis/nclavis";
import file from "../../utils/file.util";
import * as logger from '../../utils/logger.util';
import * as mailer from '../../utils/mail.util';
import * as adviewService from '../discrepancy-report-api/adview.service'; // ADVIEW
import * as brightmountainService from '../discrepancy-report-api/brightmountain.service'; // BRIGHT MOUNTAIN
import * as nativoService from '../discrepancy-report-api/nativo.service'; // NATIVO
import * as pangleService from '../discrepancy-report-api/pangle.service'; // PANGLE
import * as rtbhouseService from '../discrepancy-report-api/rtbhouse.service'; // RTB HOUSE
import * as ttdService from '../discrepancy-report-api/ttd.service'; // TTD

const REPORT_API_TYPE = COMMON_CODE.codeEncAvailable()['ReportApiType'];

const ObjectId = mongoose.Types.ObjectId;

const LOGGER = 'discrepancy_report_api';

// reportApiType 별 Service 정보
const apService = {
	[REPORT_API_TYPE.TTD.code]: ttdService,
	[REPORT_API_TYPE.NATIVO.code]: nativoService,
	[REPORT_API_TYPE.RTBHOUSE.code]: rtbhouseService,
	[REPORT_API_TYPE.PANGLE.code]: pangleService,
	[REPORT_API_TYPE.ADVIEW.code]: adviewService,
	[REPORT_API_TYPE.BRIGHT_MOUNTAIN.code]: brightmountainService,
};


/**
 * createDiscrepancyReportSchedules : Discrepancy Report 연동 스케쥴 생성
 *
 *  PROCESS 1. 불일치 연동을 하는 AP 정보 가져오기
 *  PROCESS 2. 불일치 연동 스케쥴 정보 생성 및 DB 저장
 *
 * @param {String} ymd YYYYMMDD
 * @param reportApiTypes
 */
module.exports.createDiscrepancyReportSchedules = async (ymd, reportApiTypes) => {
	logger.debug(LOGGER, `[discrepancy-report-api.service :: createDiscrepancyReportSchedules] 호출됨 ymd= ${ymd} reportApiTypes= ${reportApiTypes}`);

	await _getDiscrepancyReportConfig(reportApiTypes);


	// [PROCESS 1] _getDiscrepancyAdProviderInfos : 불일치 연동을 하는 AP 정보 가져오기
	const discrepancyAdProviderInfos = await _getDiscrepancyAdProviderInfos({ reportApiTypes });

	// 불일치 연동 정보가 없는 경우, 종료
	if (_.isEmpty(discrepancyAdProviderInfos)) {
		return;
	}

	// [PROCESS 2] 불일치 연동 스케쥴 정보 생성 및 DB 저장
	await _upsertDiscrepancyReportSchedules(ymd, discrepancyAdProviderInfos);
};


/**
 * [스케줄 생성 - PROCESS 1] _getDiscrepancyAdProviderInfos : 불일치 연동을 하는 AP 정보 가져오기
 *
 * @param reportApiTypes
 * @returns {Promise<*>}
 * 	[ { reportApiType, period: { start, end, unit }, timezone, adProvider_ids, publisher_ids } ]
 */
const _getDiscrepancyAdProviderInfos = async ({ reportApiTypes }) => {
	logger.debug(LOGGER, '[discrepancy-report-api.service :: _getDiscrepancyAdProviderInfos] 호출됨');

	// 요청에 특정 reportApiType 가 있다면, 해당하는 type 만 조회
	const reportApiTypeFilter = reportApiTypes ? { 'reportApiType': { $in: reportApiTypes } } : {};

	// 1. 대상 adProvider 및 Publisher
	//   Environments.discrepancy-report-config 의 reportApiType 과 AdProviders.discrepancyReportApi.type 이 같은 ap 들의
	//   AdProviders.status 와 Publishers.status 가 모두 ON 인 경우
	//
	// 2. 스케줄 생성 단위
	//   Environments.discrepancy-report-config 의 reportApiType (같은 기간/디멘전/메트릭/타임존)
	//   동일한 publisher_id와 adProvider_id를 묶음
	const discrepancyAdProviderInfos = await DataEnvironments
		.aggregate()
		.match({ 'name': 'discrepancy-report-config' })
		.unwind('value')
		.project({
			_id: 0,
			reportApiType: '$value.reportApiType',
			period: '$value.period',
			dimensions: '$value.finalDimensions',
			metrics: {
				'$map': {
					input: '$value.metrics',
					as: 'metric',
					in: '$$metric.name'
				}
			}
		})
		.match(reportApiTypeFilter)
		.lookup({
			from: 'SyncAdProviders',
			foreignField: 'discrepancyReportApi.type',
			localField: 'reportApiType',
			as: 'adProvider'
		})
		.unwind('adProvider')
		.match({ 'adProvider.status': 'ON' })
		.lookup({
			from: 'SyncAdProviderInfos',
			foreignField: 'adProvider_id',
			localField: 'adProvider._id',
			as: 'adProviderInfo'
		})
		.unwind('adProviderInfo')
		.lookup({
			from: 'SyncPublishers',
			foreignField: '_id',
			localField: 'adProviderInfo.publisher_id',
			as: 'publisher'
		})
		.unwind('publisher')
		.match({ 'publisher.status': 'ON' })
		.group({
			_id: {
				reportApiType: '$reportApiType',
				timezone: '$adProvider.timezone',
				period: '$period',
				dimensions: '$dimensions',
				metrics: '$metrics'
			},
			adProvider_ids: { $addToSet: '$adProvider._id' },
			publisher_ids: { $addToSet: '$publisher._id' },
		})
		.project({
			_id: 0,
			timezone: '$_id.timezone',
			reportApiType: '$_id.reportApiType',
			period: '$_id.period',
			dimensions: '$_id.dimensions',
			metrics: '$_id.metrics',
			adProvider_ids: 1,
			publisher_ids: 1,
		})
		.exec();

	logger.info(LOGGER, `[discrepancy-report-api.service :: _getDiscrepancyAdProviderInfos] 생성 대상 정보 ${JSON.stringify(discrepancyAdProviderInfos, null, 2)}`)

	return discrepancyAdProviderInfos;
};


/**
 * [스케줄 생성 - PROCESS 2] _upsertDiscrepancyReportSchedules : 불일치 연동 스케쥴 정보 생성 및 DB 저장
 *
 * @param {String} ymd
 * @param {Promise<any>} discrepancyAdProviderInfos 불일치 생성 관련 정보
 * 	[ { reportApiType, period: { start, end, unit }, timezone, adProvider_ids, publisher_ids } ]
 */
const _upsertDiscrepancyReportSchedules = async (ymd, discrepancyAdProviderInfos) => {
	logger.debug(LOGGER, '[discrepancy-report-api.service :: _upsertDiscrepancyReportSchedules] 호출됨');

	// bulkWrite operations
	const operations = new Array();

	for (const { reportApiType, period: { start, end, unit }, timezone, adProvider_ids, publisher_ids } of discrepancyAdProviderInfos) {
		operations.push({
			updateOne: {
				filter: {
					ymd, reportApiType, timezone
				},
				update: {
					$set: {
						adProvider_ids,
						publisher_ids,
						startDate: moment(ymd).add(start, unit).format('YYYYMMDD'),
						endDate: moment(ymd).add(end, unit).format('YYYYMMDD'),
						sparkState: 'READY',
						apiState: 'READY',
						result: null,
						begunAt: null,
						endedAt: null,
						modifiedAt: new Date()
					},

					// insert 시에만
					$setOnInsert: {
						createdAt: new Date()
					},
				},
				upsert: true,
			}
		});
	}

	if (!_.isEmpty(operations)) {
		const result = await DiscrepancyReportSchedule.bulkWrite(operations);

		// [ERROR] 응답 오류가 있는 경우, 에러 처리
		if (result.ok !== 1 || !_.isEmpty(result.writeErrors)) {
			throw new BusinessError(
				{ message: `[discrepancy-report-api.service :: _upsertDiscrepancyReportSchedules] DB 저장 오류` },
				{ err: JSON.stringify(result, null, 2) }
			);
		}
	}
};


/**
 * [리포트 연동 - PROCESS 1]
 * getDiscrepancyReportSchedules : 불일치 리포트 연동 스케쥴 가져오기
 *
 * @param {Object} { schedule_id, ymd, reportApiType }
 * @return {Array} discrepancyReportSchedules
 */
module.exports.getDiscrepancyReportSchedules = async ({ schedule_id, ymd, reportApiType = '' }) => {
	logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReportSchedules] 불일치 리포트 연동 스케쥴 가져오기 ( schedule_id= ${schedule_id}, ymd= ${ymd}, reportApiType= ${reportApiType} )`);

	// 스케쥴 조회 조건
	// - 날짜 : ymd
	// - sparkState=COMPLETE 이면서, apiState=READY or FAILURE 인 스케쥴 대상
	const params = {
		ymd: moment(ymd).format('YYYYMMDD'),
		reportApiType: (_.isEmpty(reportApiType)) ? { $regex: `.*${reportApiType}.*` } : { $eq: reportApiType },
		sparkState: 'COMPLETE',
		apiState: { $in: ['READY', 'FAILURE'] }
	};

	if (!_.isEmpty(schedule_id)) {
		params._id = ObjectId(schedule_id);
	}

	const discrepancyReportSchedules = await DiscrepancyReportSchedule
		.aggregate()
		.match(params)
		.project({
			_id: 1,
			ymd: 1,
			reportApiType: 1,
			adProvider_ids: 1,
			publisher_ids: 1,
			dimensions: 1,
			metrics: 1,
			startDate: 1,
			endDate: 1,
		})
		.exec();

	return discrepancyReportSchedules;
};


/**
 * [리포트 연동 - PROCESS 2, 3-1, 3-3]
 * updateDiscrepancyReportSchedulesState : 불일치 리포트 스케쥴 상태(apiState) 변경하기
 *
 * @param {Object} { discrepancyReportSchedules, apiState }
 */
module.exports.updateDiscrepancyReportSchedulesState = async ({ discrepancyReportSchedules, apiState }) => {
	logger.debug(LOGGER, '[discrepancy-report-api.service :: updateDiscrepancyReportSchedulesState] 호출됨');

	const updateOption = { $set: { apiState, modifiedAt: new Date() } };

	if (apiState === 'IN_PROGRESS') updateOption.$set.begunAt = new Date();

	if (apiState === 'FAILURE' || apiState === 'COMPLETE') {
		updateOption.$set.endedAt = new Date();
	}

	const schedule_ids = _.map(discrepancyReportSchedules, '_id');

	const result = await DiscrepancyReportSchedule.updateMany(
		{ _id: { $in: schedule_ids } },
		updateOption,
		{ multi: true, runValidators: true }
	).exec();

	// [ERROR] update 를 실패한 경우, 에러 처리
	if (result.ok !== 1) {
		throw new BusinessError(
			{ message: `[discrepancy-report-api.service :: updateDiscrepancyReportSchedulesState] DiscrepancyReportSchedule DB 저장 오류` },
			{ err: JSON.stringify(result, null, 2) }
		);
	}
};


/**
 * [리포트 연동 - PROCESS 3-2] _processDiscrepancyReportApi : ap별 불일치 리포트 연동
 *
 * @param {Object} schedule { ymd, reportApiType, reportInfos, adProvider_ids, publisher_ids, dimensions, metrics, startDate, endDate }
 * @return {Boolean} isComplete
 */
module.exports.processDiscrepancyReportApi = async (schedule) => {
	const reportApiType = schedule.reportApiType;
	const reportConfig = await _getDiscrepancyReportConfig(reportApiType);
	// reportInfos: reportApiType 별 연동 정보
	// - TTD
	// 		: { file: { path, name }, api: { bucketName, region, prefix, ACCESS_KEY, SECRET_KEY } }
	// - NATIVO, RTBHOUSE, PANGLE, ADVIEW, BRIGHTMOUNTAIN
	// 		: { file: { path, name, pwd }, mail: { addresses, subject } }
	logger.debug(LOGGER, `[discrepancy-report-api.controller :: _processDiscrepancyReportApi] 호출됨 ( schedule_id= ${schedule._id}, reportApiType= ${reportApiType} )\nreportConfig= ${JSON.stringify(reportConfig, null, 2)}`);

	try {
		// 각 AP 로 전달한 연동 대상 파일
		const localFilePath = await apService[reportApiType].processDiscrepancyReportApi(schedule, reportConfig);

		await _updateDiscrepancyReportScheduleResult({ schedule, localFilePath });

		return true;
	} catch (e) {
		logger.error(`[discrepancy-report-api.controller :: _processDiscrepancyReportApi] Error ( schedule_id= ${schedule._id}, reportApiType= ${reportApiType} ) :: \n ${e.stack}\n\n`, e);

		return false;
	}
};

/**
 * [리포트 연동 - PROCESS 3-2] updateDiscrepancyReportScheduleResult : 불일치 리포트 스케쥴 연동 결과 등록하기
 *
 * @param {Object} { schedule, localFilePath }
 */
const _updateDiscrepancyReportScheduleResult = async ({ schedule, localFilePath }) => {
	logger.debug(LOGGER, '[discrepancy-report-api.service :: updateDiscrepancyReportScheduleResult] 호출됨');

	const result = await DiscrepancyReportSchedule.updateOne(
		{ _id: schedule._id },
		{ $set: { result: localFilePath, modifiedAt: new Date() } },
		{ multi: true, runValidators: true }
	).exec();

	// [ERROR] update 를 실패한 경우, 에러 처리
	if (result.ok !== 1) {
		throw new BusinessError(
			{ message: `[discrepancy-report-api.service :: updateDiscrepancyReportScheduleResult] DiscrepancyReportSchedule DB 저장 오류` },
			{ err: JSON.stringify(result, null, 2) }
		);
	}
};

/**
 * _getDiscrepancyReportConfig : 불일치 리포트 설정 정보 가져오기
 * 	- Data > Environments : discrepancy-report-config
 *
 * @param {String} reportApiTypes
 * @return {Object} discrepancyReportConfig
 */
const _getDiscrepancyReportConfig = async reportApiTypes => {
	logger.debug(LOGGER, '[discrepancy-report-api.service :: _getDiscrepancyReportConfig] 호출됨');

	const ENV_NAME = 'discrepancy-report-config';

	const discrepancyReportConfigEnv = await DataEnvironments.findOne({ name: ENV_NAME }).exec();

	// [ERROR] 불일치 리포트 연동 설정 정보 없는 경우, 에러
	if (_.isEmpty(discrepancyReportConfigEnv) || _.isEmpty(discrepancyReportConfigEnv.value)) {
		throw new BusinessError({ message: `[discrepancy-report-api.service :: _getDiscrepancyReportConfig] Environments(${ENV_NAME}) 불일치 리포트 연동 설정 정보가 없음` });
	}

	const reportApiTypeList = _.isArray(reportApiTypes) ? reportApiTypes : [reportApiTypes];
	const discrepancyReportConfig = discrepancyReportConfigEnv.value.filter(v => _.includes(reportApiTypeList, v.reportApiType)).pop();

	// [ERROR] 불일치 리포트 연동 AP 설정 정보 없는 경우, 에러
	if (_.isEmpty(discrepancyReportConfig) || discrepancyReportConfig.length < reportApiTypeList.length ||
		_.isEmpty(discrepancyReportConfig['reportInfos']) || _.isEmpty(discrepancyReportConfig['finalDimensions'])) {
		throw new BusinessError({ message: `[discrepancy-report-api.service :: _getDiscrepancyReportConfig] Environments(${ENV_NAME}) 불일치 리포트 연동 설정 정보가 없는 type 이 있음 (reportApiType=${reportApiTypes})` });
	}

	return discrepancyReportConfig;
};


/**
 * [PROCESS 4]
 * sendFailureMail : 실패 메일 보내기
 *
 * @param {Object} sendFailureMail { _id, ymd, reportApiType, adProvider_ids, publisher_ids, startDate, endDate }
 */
module.exports.sendFailureMail = ({ _id, ymd, reportApiType, adProvider_ids, publisher_ids, startDate, endDate }) => {
	setTimeout(async () => {
		try {
			logger.debug(LOGGER, '[discrepancy-report-api.service :: sendFailureMail] 호출됨');

			const to = await _getReceiverInfo('discrepancy-report-alarm-receivers');

			const subject = `불일치 리포트 연동 실패 알림 메일 (${reportApiType}) - ${ymd}`;

			const apPubNames = await _getApPubNames({ adProvider_ids, publisher_ids });

			let html = `Schedule 정보<br><br>`
				+ `- _id: ${_id}<br>`
				+ `- ymd: ${ymd}<br>`
				+ `- reportApiType: ${reportApiType}<br>`
				+ `- period: ${startDate} ~ ${endDate}<br>`;

			if (!_.isEmpty(adProvider_ids)) {
				html += `- adProvider_ids: ${adProvider_ids}<br>`;
				html += `- adProviderNames: ${apPubNames.adProviderNames}<br>`;
			}

			if (!_.isEmpty(publisher_ids)) {
				html += `- publisher_ids: ${publisher_ids}<br>`;
				html += `- publisherNames: ${apPubNames.publisherNames}<br>`;
			}

			await mailer.sendMail({ to, subject, html });
		} catch (e) {
			logger.error(`[discrepancy-report-api.service :: sendFailureMail] Error :: \n ${e.stack}\n\n`, e);
		}
	}, 0);
};


/**
 * _getReceiverInfo : Environments에서 메일 수신자 정보 가져오기
 *
 * @param {String} env_name
 * @return {String} to
 */
const _getReceiverInfo = async (env_name) => {
	// 수신자 환경 정보 : { name: 'gfp-data-alarm-receiver', value: '<EMAIL>' }
	const receiverEnv = await DataEnvironments.findOne({ name: env_name }).exec();

	// [ERROR] 수신자 환경 정보 없는 경우, 에러
	if (_.isEmpty(receiverEnv)) {
		throw new BusinessError({ message: `[discrepancy-report-api.service :: _getReceiverInfo] Environments ${env_name} 에 수신자 정보 없음` });
	}

	let to = receiverEnv.value || [];
	to = Array.isArray(to) ? Array.from(to).join(';') : to

	logger.debug(LOGGER, `[discrepancy-report-api.service :: _getReceiverInfo] Environments ${env_name}= ${to}`);

	return to;
};


/**
 * _getApPubNames : adProvider, publisher 이름 가져오기
 *
 * @param {Object} { adProvider_ids, publisher_ids }
 * @return {Object} { adProviderNames, publisherNames }
 */
const _getApPubNames = async ({ adProvider_ids, publisher_ids }) => {
	if (_.isEmpty(adProvider_ids) && _.isEmpty(publisher_ids)) return;

	const apPubNames = {};

	const adProviders = await AdProvider.find({ _id: { $in: adProvider_ids } }).select('-_id name').exec();
	const publishers = await Publisher.find({ _id: { $in: publisher_ids } }).select('-_id name').exec();

	apPubNames.adProviderNames = (!_.isEmpty(adProviders)) ? _.map(adProviders, 'name').join(', ') : null;
	apPubNames.publisherNames = (!_.isEmpty(publishers)) ? _.map(publishers, 'name').join(', ') : null;

	return apPubNames;
};


/**
 * getDiscrepancyReport : 적재해둔 디멘전 별 데이터로부터 리포트 집계 및 메타 추가
 * 메타 정보 추가가 필요할 시, 하드코딩이 다소 필요함
 *
 * 각 reportApiType 별 service 에서 호출(processDiscrepancyReportApi)
 *
 * @param {Object} {adProvider_ids, publisher_ids, dimensions, metrics, startDate, endDate }
 * @return {Array} 연동한 adProvider 별로 원하는 디멘전/메트릭이 다름
 * 	[{ date, ... , sspEstimatedImpressions, ... }]
 */
module.exports.getDiscrepancyReport = async ({ adProvider_ids, publisher_ids, dimensions, metrics, startDate, endDate }) => {
	logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] 호출됨 ( ${startDate} ~ ${endDate} )`);

	const pipeLine = [];

	const matchSpec = {
		$match: {
			date: { $gte: startDate, $lte: endDate },
			adProvider_id: { $in: adProvider_ids.map(adProviderId => new Types.ObjectId(adProviderId)) },
			publisher_id: { $in: publisher_ids.map(publisherId => new Types.ObjectId(publisherId)) }
		}
	};
	pipeLine.push(matchSpec);
	// logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] matchSpec ( ${JSON.stringify(matchSpec, null, 2)} )`);

	const groupSpec = {
		$group: {
			// dimension
			_id: new Map(dimensions.map(dim => [dim, `$${dim}`])),

			// metric (메트릭은 집계 쿼리에 따라 다양해 일반화 하기 어려워 현재는 정적으로 작성해두는 것이 최선)
			sspEstimatedImpressions: { $sum: '$sspEstimatedImpressions' },
			sspEstimatedRevenue: { $sum: '$sspEstimatedRevenue' }
		}
	}
	pipeLine.push(groupSpec);
	// logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] groupSpec ( ${JSON.stringify(groupSpec, null, 2)} )`);

	// group 집계 결과로 부터 계산이 필요한 파생 메트릭 추가
	const addFieldsSpec = {
		$addFields: {
			sspEstimatedCpm: {
				$cond: {
					if: { $eq: ['$sspEstimatedImpressions', 0] },
					then: 0,
					else: {
						$toDouble: {
							$multiply: [{
								$divide: ['$sspEstimatedRevenue', '$sspEstimatedImpressions']
							}, 1000]
						}
					}
				}
			}
		}
	}
	pipeLine.push(addFieldsSpec);
	// logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] addFieldsSpec ( ${JSON.stringify(addFieldsSpec, null, 2)} )`);

	// 디멘전의 메타 정보 추가 (매체, 서비스 이름)
	const metaProject = {};
	if (dimensions.includes('publisher_id')) {
		pipeLine.push({
			$lookup: {
				from: 'SyncPublishers',
				foreignField: '_id',
				localField: '_id.publisher_id',
				as: 'publisher'
			}
		});
		metaProject['publisherName'] = { $ifNull: [{ $arrayElemAt: ['$publisher.name', 0] }, "-"] }
	}
	if (dimensions.includes('service_id')) {
		pipeLine.push({
			$lookup: {
				from: 'SyncPublisherServices',
				foreignField: '_id',
				localField: '_id.service_id',
				as: 'service'
			}
		});
		metaProject['serviceName'] = { $ifNull: [{ $arrayElemAt: ['$service.name', 0] }, "-"] }
	}
	// logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] lookupSpec ( ${JSON.stringify(lookupSpec, null, 2)} )`);

	const baseProject = Object.assign({ _id: 0 }, metaProject);
	const projectSpec = {
		$project: Object.assign(
			baseProject,
			...dimensions.map(dim =>
				({
					[dim]: {
						$cond: {
							// objectId 타입은 string 타입으로 변환하여 조회
							if: { $eq: [{ $type: `$_id.${dim}` }, "objectId"] },
							then: { $toString: `$_id.${dim}` },
							else: `$_id.${dim}`
						}
					}
				})
			),
			...metrics.map(met => ({ [met]: 1 }))
		)
	};
	pipeLine.push(projectSpec);
	// logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] projectSpec ( ${JSON.stringify(projectSpec, null, 2)} )`)

	const sortSpec = {
		$sort: Object.assign({}, ...dimensions.map(dim => ({ [dim]: 1 })))
	}
	pipeLine.push(sortSpec);

	const reportData = await DiscrepancyDaily.aggregate(pipeLine).exec();

	logger.debug(LOGGER, `[discrepancy-report-api.service :: getDiscrepancyReport] DiscrepancyDailyData ( ${JSON.stringify(reportData, null, 2)} )`)

	return reportData;
};

/**
 * 조회한 데이터로부터 불일치 리포트 파일 생성
 *
 * @param {Object} { _id, startDate, endDate, baseDir, fileNameFormat, pwd, reportData }
 * @param {Array} headerMappingList : [{_id: reportData 내의 대상 데이터의 컬럼명, title: 파일내의 헤더명}, {}, ..]
 * @return {String} filePath
 */
module.exports.generateReportFile = async ({ _id, startDate, endDate, baseDir, fileNameFormat, pwd, reportData }, headerMappingList) => {
	logger.debug(LOGGER, `[discrepancy-report-api.service :: _generateReportFile] 불일치 리포트 파일 생성하기 ( schedule_id= ${_id})`);

	// discrepancy 디렉토리 생성
	await file.mkdirIfNotExist(baseDir);

	// file_name: 'DiscrepancyReport_{{startDate}}_{{endDate}}.csv'
	// YYYY-MM-DD = 베치 일자 (ymd) / hh-mm-ss = 배치 처리 시각 (current time)
	const fileName = fileNameFormat.replace('{{startDate}}', startDate).replace('{{endDate}}', endDate).replace(/\s/g, '');

	// /home1/irteam/deploy/download/discrepancy_report/{reportApiType}/yyyy/mm/DiscrepancyReport_{{startDate}}_{{endDate}}.csv
	const filePath = path.join(baseDir, fileName);
	// 암호화 된 압축 파일 경로(메일 첨부용)
	const zipFilePath = `${filePath}.zip`

	// csv 파일 쓰기
	const csvWriter = createObjectCsvWriter({
		header: headerMappingList,
		path: filePath
	});
	await csvWriter.writeRecords(reportData);

	// BOM 추가
	const fileContents = fs.readFileSync(filePath);
	fs.writeFileSync(filePath, "\ufeff" + fileContents);

	// 암호화된 zip 파일 생성
	await _makeEncryptedZipFile(filePath, zipFilePath, pwd);

	return zipFilePath;
};

/**
 * 메일로 불일치 리포트를 전달하는 경우, 파일 암호화를 위해 zip 파일을 생성
 * 암호는 미리 nclavis 로 암호화하여 관리한다고 가정
 *
 * 	zip 커맨드 옵션
 * 	 -j: 압축 대상이 되는 파일의 leaf 파일만 압축하도록 설정 (중간 경로 생략)
 * 	 -P: 파일의 비밀번호 설정 (압축 해제시 입력해야 함)
 * 	 -b: 별도의 임시경로 설정
 *
 * @param srcFilePath
 * @param dstFilePath
 * @param encryptedPwd
 * @returns {Promise<void>}
 */
const _makeEncryptedZipFile = async (srcFilePath, dstFilePath, encryptedPwd) => {
	// 사전에 encrypt 하여 관리하는 암호를 nclavis 를 통해 실제 암호로 변환
	const originPwd = await nclavis.decryptInline(encryptedPwd);

	const encryptZipCmd = `zip -j -b ${config.report.discrepancy.tmp_for_zip} -P ${originPwd} ${dstFilePath} ${srcFilePath}`;
	const cmdForLog = `zip -j -P ${encryptedPwd} ${dstFilePath} ${srcFilePath}`;
	try {
		const stdout = await child_process.execSync(encryptZipCmd, { encoding: 'utf8' });
		logger.debug(LOGGER, `[discrepancy-report-api.service :: makeEncryptedZipFile] cmd= ${cmdForLog} stdout= ${stdout}`);
		if (stdout.includes('zip warning')) {
			// 경고 메시지는 stdout 으로 출력되나, 정상동작하지 않는 경우가 있으므로 따로 처리
			throw new BusinessError({ message: '파일 압축에 문제가 발생했습니다.' }, stdout);
		}
	} catch (err) {
		logger.error(`[discrepancy-report-api.service :: makeEncryptedZipFile] cmd= ${cmdForLog} err= ${err.stack}`);
		throw err;
	}
}

/**
 * 메일을 발송하여 리포트 연동. 리얼 환경에서만 메일을 보냄
 *
 * @param {Object} { _id, filePath, mail }
 */
module.exports.sendReport = async ({ _id, filePath, addresses, subject }) => {
	logger.debug(LOGGER, `[discrepancy-report-api.service :: _sendReport] 리포트 메일 전송 시작 ( schedule_id= ${_id} )`);

	if (process.env.NODE_ENV === 'production') {
		for (const address of addresses) {
			const decryptedAddress = (address.endsWith('@navercorp.com')) ? address : await nclavis.decryptInline(address);
			logger.debug(LOGGER, `[discrepancy-report-api.service :: _sendReport] address= ${decryptedAddress}`);

			await mailer.sendMail({
				to: decryptedAddress,
				subject: subject,
				isBatchNoti: false,
				attachments: [{
					filename: filePath.base,
					path: filePath
				}]
			});
		}
	}

	logger.debug(LOGGER, `[discrepancy-report-api.service :: _sendReport] 리포트 메일 전송 완료 ( schedule_id= ${_id} )`);
};
