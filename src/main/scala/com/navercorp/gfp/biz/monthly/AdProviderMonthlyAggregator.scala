package com.navercorp.gfp.biz.monthly

import java.util.Date
import scala.concurrent.ExecutionContext.Implicits.global
import scala.concurrent.Future
import scala.util.Try

import com.mongodb.spark.sql.toMongoDataFrameReaderFunctions
import org.apache.hadoop.fs.FileSystem
import org.apache.spark.sql._
import org.apache.spark.sql.functions._
import org.joda.time.format.DateTimeFormat

import com.navercorp.gfp.core.BaseEnv.{AMBER_ROOT, MONTHLY_ROOT}
import com.navercorp.gfp.core.summaryhistory.{Spark, SparkAppState, SummaryHistory, SummaryHistoryDetail}
import com.navercorp.gfp.core.{BaseAggregator, BaseEnv, BizAggregator, DeleteFutureHelper}
import com.navercorp.gfp.exception.{BusinessException, BusinessExceptionType}
import com.navercorp.gfp.util.{HdfsUtil, NeloUtil}

/*
	- amber 기반의 광고공급자 월별 리포트 생성
		- input amber : /data/log/gfp/amber/{ap,gfp}/yyyy/mm/*/adProviderId=*/publisherId=*
		- output csv : /data/log/gfp/monthly/yyyy/mm/yyyymm_adprovider.csv

	spark 설정
		--num-executors 24
		--executor-cores 3
		--executor-memory 600m
		--conf spark.sql.shuffle.partitions=5
		--conf spark.driver.memoryOverhead=1g
		--conf spark.executor.memoryOverhead=500m
*/
object AdProviderMonthlyAggregator extends BaseAggregator {
	val LOG_PREFIX = ".......... [Monthly-AP]"

	private var sparkAppId: String = ""

	def main(rawArgs: Array[String]): Unit = {
		val args = initArgs(rawArgs)

		// 처리 대상 월
		val yyyymm = args(0)

		try {
			// args 밸리데이션 체크
			// require 는 runtime 시점에 Exception 을 던진다.
			// Exception 없이, 인스턴스 생성 시점에 validation 체크를 하고 싶으면 apply 를 구현하도록 한다.
			//  - https://gist.github.com/jkpl/4932e8730c1810261381851b13dfd29d
			//  - https://www.47deg.com/blog/smart-constructors-in-scala/#smart-constructors-for-case-classes-0
			//  - https://stackoverflow.com/questions/5982484/scala-lift-check-if-date-is-correctly-formatted
			require(Try(DateTimeFormat.forPattern("yyyyMM").parseDateTime(yyyymm)).isSuccess, s"yyyymm($yyyymm) is invalid format (must be yyyyMM)")


			// 이력쌓기 - 진행중 (IN_PROGRESS)
			sparkAppId = spark.sparkContext.applicationId
			val inProgressHist = SummaryHistory(
				datetime = Option(yyyymm),
				aggregatorName = Option(this.getClass.getName),
				spark = Option(Spark(
					sparkAppId = Option(sparkAppId),
					sparkAppState = Option(SparkAppState.IN_PROGRESS.toString),
					sparkStartedAt = Option(new Date)
				))
			)
			this.upsertSummaryHistory(inProgressHist)

			// amber 경로 가져오기
			val pathList = getPathList(yyyymm)

			// amber 로딩
			val amberDf = loadParquetLog(pathList) match {
				case Some(df) => df
				case _ => throw BusinessException("pathList is empty", BusinessExceptionType.EmptyPath)
			}

			// 집계 인스턴스 생성
			val aggregator = new AdProviderMonthlyAggregator(amberDf, yyyymm)

			val aggregatedDf = aggregator.aggregate()

			// monthlyRoot= /data/log/gfp/monthly/yyyy/mm
			// tempPath= /data/log/gfp/monthly/yyyy/mm/adprovider
			// finalPath= /data/log/gfp/monthly/yyyy/mm/yyyymm_adprovider.csv
			// successFilePath= /data/log/gfp/monthly/yyyy/mm/_AP_SUCCESS
			val monthlyRoot = s"$MONTHLY_ROOT/${yyyymm.substring(0, 4)}/${yyyymm.substring(4, 6)}"
			val tempPath = s"$monthlyRoot/adprovider"
			val finalPath = s"$monthlyRoot/${yyyymm}_adprovider.csv"
			val successFilePath = s"$monthlyRoot/_AP_SUCCESS"

			// HDFS - 기존 파일 삭제
			aggregator.delete(aggregator.getFuturesForDelete(Option((hdfs, tempPath, finalPath, successFilePath))))

			// HDFS - csv 파일 쓰기
			aggregator.write(aggregatedDf, Option((hdfs, tempPath, finalPath)))

			// 캐시 제거
			aggregator.unpersist()

			// AP Monthly successFile 생성
			createSuccessFile(successFilePath)

			// 이력쌓기 - 완료 (COMPLETE)
			val completeHist = SummaryHistory(
				spark = Option(Spark(
					sparkAppState = Option(SparkAppState.COMPLETE.toString),
					sparkEndedAt = Option(new Date)
				)),
				detail = Option(SummaryHistoryDetail(filePaths = Option(Seq(finalPath))))
			)
			this.upsertSummaryHistory(completeHist)
		} catch {
			// Throwable 클래스는 예외 처리를 할 수 있는 최상위 클래스이다. Exception 과 Error 는 Throwable 를 상속 받는다. ( https://sjh836.tistory.com/122 )
			case t: Throwable =>
				// 이력쌓기 - 실패 (FAILURE)
				val failureHist = SummaryHistory(
					spark = Option(Spark(
						sparkAppState = Option(SparkAppState.FAILURE.toString),
						sparkAppError = Option(t.getMessage),
						sparkEndedAt = Option(new Date)
					))
				)
				this.upsertSummaryHistory(failureHist)

				t match {
					case BusinessException(_, _) =>
						logger.warn(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
					case _ =>
						logger.error(s"$LOG_PREFIX [summaryHistoryId=${summaryHistoryId.getOrElse("")}][sparkAppId=$sparkAppId] ${t.getMessage}", t)
						throw t
				}
		} finally {
			NeloUtil.waitFor()
		}
	}

	/**
	 * amber 경로 가져오기
	 *
	 * @param yyyymm
	 * @return pathList
	 */
	def getPathList(yyyymm: String): Seq[String] = {
		val ym = DateTimeFormat.forPattern("yyyyMM").parseDateTime(yyyymm)
		val path: String = s"$AMBER_ROOT/{ap,gfp}/${ym.toString("yyyy/MM")}/*/adProviderId=*/publisherId=*"

		// hdfs 에 존재하는 경로만 따로 추출
		val pathList = if (HdfsUtil.existsPathPattern(hdfs, path)) Seq(path) else Seq()

		//    logger.debug(s"pathList $pathList")

		pathList
	}

	/**
	 * 로그 로딩
	 *
	 * @param pathList
	 * @return Dataset[Row]
	 */
	override def loadParquetLog(pathList: Seq[String]): Option[Dataset[Row]] = {
		// HDFS 경로가 하나도 없는 경우, None
		if (pathList.isEmpty) {
			None
		} else {
			super.loadParquetLog(pathList)
		}
	}

	/**
	 * SuccessFile 생성
	 *
	 * @param successFile
	 */
	def createSuccessFile(successFile: String): Unit = {
		HdfsUtil.create(hdfs, successFile)
	}
}

/**
 * 데이터 삭제 / 집계 / 추가 등의 작업을 한다.
 *
 * spark-submit에서 호출할 때나 test case에서 호출할 때 사용함
 * 환경에 따라 달라지는 값은 생성자 파라미터로 받아서 처리함.
 *
 * @param amberDf
 * @param yyyymm
 */
class AdProviderMonthlyAggregator(amberDf: Dataset[Row], yyyymm: String)(implicit spark: SparkSession) extends BizAggregator with DeleteFutureHelper with MonthlyHelper {
	val LOG_PREFIX = ".......... [Monthly-AP]"

	import spark.implicits._

	private val baseGroupByList = Seq("type", "publisherId", "adProviderId", "adProviderPlaceKey", "country", "adUnitIds")
	private val groupByList = baseGroupByList ++ Seq("adProviderPlaceIds")
	private val selectList = groupByList ++ Seq("impressions", "clicks", "netRevenueUSD", "netRevenueKRW", "revenueUSD", "revenueKRW", "gfpImpressions", "gfpViewableImpressions", "gfpClicks", "gfpEstimatedImpressions")
	private val aggList = Seq(
		expr("NVL(SUM(impressions), 0)").as("impressions"),
		expr("NVL(SUM(clicks), 0)").as("clicks"),
		expr("NVL(SUM(netRevenueUSD), 0)").as("netRevenueUSD"),
		expr("NVL(SUM(netRevenueKRW), 0)").as("netRevenueKRW"),
		expr("NVL(SUM(revenueUSD), 0)").as("revenueUSD"),
		expr("NVL(SUM(revenueKRW), 0)").as("revenueKRW"),
		expr("NVL(SUM(gfpImpressions), 0)").as("gfpImpressions"),
		expr("NVL(SUM(gfpViewableImpressions), 0)").as("gfpViewableImpressions"),
		expr("NVL(SUM(gfpClicks), 0)").as("gfpClicks"),
		expr("NVL(SUM(gfpEstimatedImpressions) ,0)").as("gfpEstimatedImpressions"),
	)

	private val finalGroupByList = baseGroupByList ++ Seq(
		"corporationId", "corporationName", "minPaymentCurrency",
		"publisherName", "corporationType", "sdkServingType", "serviceIds", "serviceNames",
		"adProviderName", "connectionType", "reportApiSource", "billingGroup", "currency", "timezone",
		"channelTypes", "monetizedCountryType"
	)

	private val terms = Vector(
		("corporationId", "Corporation ID"),
		("corporationName", "Corporation Name"),
		("minPaymentCurrency", "Corporation Currency"),

		("publisherId", "Publisher ID"),
		("publisherName", "Publisher Name"),
		("corporationType", "Publisher Client Type"),
		("sdkServingType", "SDK Serving Type"),

		("serviceIds", "Service IDs"),
		("serviceNames", "Service Names"),

		("adProviderId", "AdProvider ID"),
		("adProviderName", "AdProvider Name"),
		("connectionType", "AdProvider Connection Type"),
		("type", "AdProvider Stats Type"),
		("reportApiSource", "AdProvider Report Type"),
		("billingGroup", "AdProvider Billing group"),
		("currency", "AdProvider Currency"),
		("timezone", "AdProvider Timezone"),

		("adProviderPlaceKey", "Place Key"),

		("adUnitIds", "AdUnit IDs"),
		("channelTypes", "Channel Types"),

		("country", "Country"),
		("monetizedCountryType", "Monetized Country Type"),

		("impressions", "AP Impressions"),
		("clicks", "AP Clicks"),
		("netRevenueUSD", "AP Net Revenue USD"),
		("netRevenueKRW", "AP Net Revenue KRW"),
		("revenueUSD", "AP Revenue USD"),
		("revenueKRW", "AP Revenue KRW"),

		("gfpImpressions", "GFP Impressions"),
		("gfpViewableImpressions", "GFP Viewable Impressions"),
		("gfpClicks", "GFP Clicks"),
		("gfpEstimatedImpressions", "Estimated Impressions"), // 수익쉐어리포트에서도 'gfp' prefix 없이 사용되므로 prefix 제거
	)

	var cachedPubDf: Dataset[Row] = _

	/**
	 * 데이터 집계
	 *
	 * @return finalDf Dataset[Row]
	 */
	def aggregate(aggParam: Option[A] = None): Dataset[Row] = {
		// amber 로그에서 가져온 데이터
		val aggDf = amberDf
			.selectExpr(selectList: _*)
			.groupBy(groupByList.head, groupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)

		// 메타 정보 추가
		val addedDf1 = addMetaOfPublishers(aggDf)
		val addedDf2 = addMetaOfCorporations(addedDf1)
		val addedDf3 = addMetaOfAdUnits(addedDf2)
		val addedDf4 = addMetaOfServices(addedDf3)
		val addedDf5 = addMetaOfAdProviders(addedDf4)
		val addedDf6 = addMetaOfAdProviderPlaces(addedDf5)
		val addedDf7 = convertArrayToString(addedDf6)

		// 최종 집계 (adProviderPlaceIds 로 인해 dimension 이 중복 되는 케이스가 있음)
		val finalAggDf = addedDf7
			.selectExpr(addedDf7.columns: _*)
			.groupBy(finalGroupByList.head, finalGroupByList.tail: _*)
			.agg(aggList.head, aggList.tail: _*)

		val finalDf = renameColumn(finalAggDf, terms)

		//		logger.debug("finalDf schema >>>>>>")
		//		finalDf.printSchema()
		//		finalDf.show(300, false)
		//		finalDf.explain()

		finalDf
	}

	/**
	 * 퍼블리셔 메타 정보 추가
	 * - publisherId :: publisherName / corporationId / corporationType / sdkServingType / monetizedCountryType
	 *
	 * @param aggDf
	 * @return
	 */
	def addMetaOfPublishers(aggDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX publisherName / corporationId / corporationType / sdkServingType / monetizedCountryType 추가")
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncPublishers")
		val pubDf = spark
			.read
			.mongo(readConfig)
			.select(
				$"_id.oid".as("publisherId"),
				$"name".as("publisherName"),
				$"corporation_id.oid".as("corporationId"),
				$"corporationType",
				$"sdkServingType",
				$"monetizedCountryType"
			)

		val addedDf = aggDf.as("agg")
			.join(pubDf.as("pub"), $"agg.publisherId" === $"pub.publisherId", "left_outer")
			.select(
				"agg.*",
				"pub.publisherName",
				"pub.corporationId",
				"pub.corporationType",
				"pub.sdkServingType",
				"pub.monetizedCountryType"
			)
		addedDf
	}

	/**
	 * 거래처 메타 정보 추가
	 * 	- corporationId :: corporationName / minPaymentCurrency
	 *
	 * @param aggDf
	 * @return
	 */
	def addMetaOfCorporations(aggDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX corporationName / minPaymentCurrency 추가")
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncCorporations")
		val corpDf = spark.read
			.mongo(readConfig)
			.select(
				$"_id.oid".as("corporationId"),
				$"name".as("corporationName"),
				$"minPaymentCurrency")

		val addedDf = aggDf.as("agg")
			.join(corpDf.as("corp"), $"agg.corporationId" === $"corp.corporationId", "left")
			.select(
				"agg.*",
				"corp.corporationName",
				"corp.minPaymentCurrency",
			)
		addedDf
	}

	/**
	 * 광고유닛 메타 정보 추가
	 * - adUnitIds :: serviceIds
	 *
	 * @param aggDf
	 * @return
	 */
	def addMetaOfAdUnits(aggDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX serviceIds 추가")
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdUnits")
		val auDf = spark.read
			.mongo(readConfig)
			.select(
				$"adUnitId",
				$"service_id.oid".as("serviceId"),
			)

		val addedDf = aggDf.withColumn("adUnitId", explode_outer($"adUnitIds")).as("agg")
			.join(auDf.as("au"), $"agg.adUnitId" === $"au.adUnitId", "left_outer")
			.groupBy(aggDf.columns.head, aggDf.columns.tail: _*)
			.agg(
				collect_set("au.serviceId").alias("serviceIds")
			)
		addedDf
	}

	/**
	 * 서비스 메타 정보 추가
	 * 	- serviceIds :: serviceNames
	 *
	 * @param aggDf
	 * @return
	 */
	def addMetaOfServices(aggDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX serviceNames 추가")
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncPublisherServices")
		val svcDf = spark.read
			.mongo(readConfig)
			.select(
				$"_id.oid".as("serviceId"),
				$"name".as("serviceName")
			)

		val addedDf = aggDf.withColumn("serviceId", explode_outer($"serviceIds")).as("agg")
			.join(svcDf.as("svc"), $"agg.serviceId" === $"svc.serviceId", "left_outer")
			.groupBy(aggDf.columns.head, aggDf.columns.tail: _*)
			.agg(
				collect_set("svc.serviceName").alias("serviceNames")
			)
		addedDf
	}

	/**
	 * 광고공급자 메타 정보 추가
	 * 	- adProviderId :: adProviderName / connectionType / billingGroup / currency / timezone
	 *
	 * @param aggDf
	 * @return
	 */
	def addMetaOfAdProviders(aggDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX adProviderName / connectionType / billingGroup / currency / timezone 추가")
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviders")
		val apDf = spark.read
			.mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderId"),
				$"name".as("adProviderName"),
				$"connectionType",
				$"reportApi.source".as("reportApiSource"),
				$"billingGroup",
				$"currency",
				$"timezone"
			)

		val addedDf = aggDf.as("agg")
			.join(apDf.as("ap"), $"agg.adProviderId" === $"ap.adProviderId", "left_outer")
			.select(
				"agg.*",
				"ap.adProviderName",
				"ap.connectionType",
				"ap.reportApiSource",
				"ap.billingGroup",
				"ap.currency",
				"ap.timezone",
			)
		addedDf
	}


	/**
	 * 광고공급단위 메타 정보 추가
	 * 	- adProviderPlaceIds :: channelTypes
	 *
	 * @param aggDf
	 * @return
	 */
	def addMetaOfAdProviderPlaces(aggDf: Dataset[Row]): Dataset[Row] = {
		logger.debug(s"$LOG_PREFIX channelTypes 추가")
		val readConfig = BaseEnv.mdbDefaultReadConfig.withOption("spark.mongodb.input.collection", "SyncAdProviderPlaces")
		val appDf = spark.read
			.mongo(readConfig)
			.select(
				$"_id.oid".as("adProviderPlaceId"),
				$"channelType",
			)

		val addedDf = aggDf.withColumn("adProviderPlaceId", explode_outer($"adProviderPlaceIds")).as("agg")
			.join(appDf.as("app"), $"agg.adProviderPlaceId" === $"app.adProviderPlaceId", "left_outer")
			.groupBy(aggDf.columns.head, aggDf.columns.tail: _*)
			.agg(
				collect_set("app.channelType").alias("channelTypes")
			)
			.drop("adProviderPlaceIds")
		addedDf
	}

	/**
	 * Array 메타 정보들 String 으로 변환
	 *
	 * @param df
	 * @return
	 */
	def convertArrayToString(df: Dataset[Row]): Dataset[Row] = {
		val df1 = df
			.withColumn("adUnitIds", concat_ws(",", $"adUnitIds"))
			.withColumn("serviceIds", concat_ws(",", $"serviceIds"))
			.withColumn("serviceNames", concat_ws(",", $"serviceNames"))
			.withColumn("channelTypes", concat_ws(",", $"channelTypes"))

		df1
	}

	/**
	 * 기존 파일들 삭제
	 */
	def getFuturesForDelete(param: Option[T]): List[Future[Option[Boolean]]] = {
		val (hdfs, tempPath, finalPath, successFilePath) = param.get

		logger.debug(s"$LOG_PREFIX tempPath= $tempPath, finalPath= $finalPath, successFilePath= $successFilePath")

		List(Future {
			HdfsUtil.delete(hdfs, tempPath)
			HdfsUtil.delete(hdfs, finalPath)
			HdfsUtil.delete(hdfs, successFilePath)

			Option(true)
		})
	}

	type T = (FileSystem, String, String, String) // ABSTRACT TYPE MEMBERS :: https://docs.scala-lang.org/tour/abstract-type-members.html

	/**
	 * 리포트 파일 생성 및 경로 이동
	 *
	 * @param df
	 * @param writeParam
	 */
	def write(df: DataFrame, writeParam: Option[W] = None): Unit = {
		val (hdfs, tempPath, finalPath) = writeParam.get

		logger.debug(s"$LOG_PREFIX tempPath= $tempPath, finalPath= $finalPath")

		this.writeCsvToHdfs(df, tempPath)
		moveFileWithBOM(hdfs, tempPath, finalPath)
	}

	type W = (FileSystem, String, String)

	/**
	 * 캐시 제거
	 */
	def unpersist(): Unit = {
		if (cachedPubDf != null) cachedPubDf.unpersist()
	}
}
