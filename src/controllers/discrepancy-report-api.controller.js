'use strict';

import COMMON_CODE from '@ssp/ssp-common-code';
import _ from 'lodash';
import moment from 'moment';
import mongoose from 'mongoose';
import { BusinessError } from '../common/error';

import * as discrepancyReportApiService from '../services/discrepancy-report-api/discrepancy-report-api.service';

import * as logger from '../utils/logger.util';

const REPORT_API_TYPE = COMMON_CODE.codeEncAvailable()['ReportApiType'];

const LOGGER = 'discrepancy_report_api';

// Discrepancy AdProvider 코드 정보
// TTD, NATIVO, RTBHOUSE, PANGLE, ADVIEW, BRIGHT_MOUNTAIN
const DISCREPANCY_AP = [
	REPORT_API_TYPE.TTD.code,
	REPORT_API_TYPE.NATIVO.code,
	REPORT_API_TYPE.RTBHOUSE.code,
	REPORT_API_TYPE.PANGLE.code,
	REPORT_API_TYPE.ADVIEW.code,
	REPORT_API_TYPE.BRIGHT_MOUNTAIN.code
];


/**
 * createDiscrepancyReportSchedules : Discrepacny Report 연동 스케쥴 생성
 *
 * @RequestMapping(value='/batch/discrepancy/report/schedule')
 */
module.exports.createDiscrepancyReportSchedules = async (ctx) => {
	logger.debug(LOGGER, `[discrepancy-report-api.controller :: createDiscrepancyReportSchedules] ${ctx.path} 호출됨`);

	const reportApiTypes = ctx.request.query.reportApiTypes ?
		ctx.request.query.reportApiTypes.split(',').map(type => type.toUpperCase()) : DISCREPANCY_AP;

	// 불일치 처리 대상 AdProvider 가 아닌 경우
	if (!_.isEmpty(reportApiTypes) && reportApiTypes.some(type => !_.includes(DISCREPANCY_AP, type))) {
		throw new BusinessError({ message: `Discrepancy Report 처리 대상이 아닌 report type 이 포함되어 있습니다. (${reportApiTypes})` });
	}

	const body = {
		code: 200,
		message: 'createDiscrepancyReportSchedules 요청 완료'
	};

	try {
		// 처리 날짜 정보
		const ymd = ctx.request.query.ymd || moment().format('YYYYMMDD');

		// DiscrepancyReportSchedule 생성
		await discrepancyReportApiService.createDiscrepancyReportSchedules(ymd, reportApiTypes);

		logger.debug(LOGGER, '[discrepancy-report-api.controller :: createDiscrepancyReportSchedules] 처리 완료');
	} catch (e) {
		logger.error(`[discrepancy-report-api.controller :: createDiscrepancyReportSchedules] Error :: \n ${e.stack} \n`, e);

		body.code = 500;
		body.message = 'createDiscrepancyReportSchedules 요청 실패';
	} finally {
		ctx.body = body;
	}
};


/**
 * processDiscrepancyReportApi : Discrepacny Report API 연동
 *
 * @RequestMapping(value='/batch/discrepancy/report/api/:reportApiType')
 */
module.exports.processDiscrepancyReportApi = async (ctx) => {
	logger.debug(LOGGER, `[discrepancy-report-api.controller :: processDiscrepancyReportApi] ${ctx.path} 호출됨`);

	const reportApiType = ctx.params.reportApiType.toUpperCase();

	// 불일치 처리 대상 AdProvider 가 아닌 경우
	if (!_.includes(DISCREPANCY_AP, reportApiType)) {
		throw new BusinessError({ message: 'Discrepancy Report 처리 대상이 아닙니다.' });
	}

	const body = {
		code: 200,
		message: 'processDiscrepancyReportApi 요청 완료'
	};

	try {
		const ymd = ctx.request.query.ymd || moment().format('YYYYMMDD');
		const schedule_id = ctx.request.query.schedule_id || '';

		if (!_.isEmpty(schedule_id) && !mongoose.isValidObjectId(schedule_id)) {
			throw new BusinessError({ message: `[discrepancy-report-api.controller :: processDiscrepancyReportApi] schedule_id is invalid ( schedule_id= ${schedule_id} )` });
		}


		// [PROCESS 1] 불일치 리포트 연동 가능한 상태인 스케쥴 가져오기
		const discrepancyReportSchedules = await discrepancyReportApiService.getDiscrepancyReportSchedules({ schedule_id, ymd, reportApiType });

		if (_.isEmpty(discrepancyReportSchedules)) {
			logger.debug(LOGGER, '[discrepancy-report-api.controller :: processDiscrepancyReportApi] 처리할 스케쥴 정보 없음');

			return;
		}


		// [PROCESS 2] 스케쥴 상태(apiState)를 모두 WAIT 으로 변경
		await discrepancyReportApiService.updateDiscrepancyReportSchedulesState({ discrepancyReportSchedules, apiState: 'WAIT' });


		// [PROCESS 3] discrepancyReportSchedules 별로 처리
		for (let schedule of discrepancyReportSchedules) {
			// [PROCESS 3-1] 스케쥴 상태를 IN_PROGRESS 로 변경
			await discrepancyReportApiService.updateDiscrepancyReportSchedulesState({ discrepancyReportSchedules: [schedule], apiState: 'IN_PROGRESS' });

			// [PROCESS 3-2] reportApiType 별로 불일치 연동
			const isComplete = await discrepancyReportApiService.processDiscrepancyReportApi(schedule);

			// [PROCESS 3-3] 스케쥴 상태를 COMPLETE/FAILURE 로 변경
			await discrepancyReportApiService.updateDiscrepancyReportSchedulesState({ discrepancyReportSchedules: [schedule], apiState: (isComplete) ? 'COMPLETE' : 'FAILURE' });

			// [PROCESS 3-4] 실패인 경우, 알림 메일 보내기
			if (isComplete === false) {
				discrepancyReportApiService.sendFailureMail(schedule);
			}
		}

		logger.debug(LOGGER, '[discrepancy-report-api.controller :: processDiscrepancyReportApi] 처리 완료');
	} catch (e) {
		logger.error(`[discrepancy-report-api.controller :: processDiscrepancyReportApi] Error :: \n ${e.stack} \n`, e);

		body.code = 500;
		body.message = 'processDiscrepancyReportApi 요청 실패';
	} finally {
		ctx.body = body;
	}
};
