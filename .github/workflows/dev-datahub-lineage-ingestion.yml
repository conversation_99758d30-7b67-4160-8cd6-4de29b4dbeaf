name: (DEV) Datahub file base lineage ingest

on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - 'airplant/data-hub/**'

jobs:
  deploy:
    runs-on: [self-hosted, navix-8, dev]
    environment: dev

    steps:
      - name: Checkout code from pr branch
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}

      - name: Install dependencies
        run: |
          python3 -m venv venv
          source venv/bin/activate
          python3 -m pip install --upgrade wheel setuptools
          pip3 install --upgrade acryl-datahub
          pip3 install 'acryl-datahub[datahub-lineage-file]'

      - name: Merge lineage files
        run: |
          sh ${GITHUB_WORKSPACE}/airplant/data-hub/script/merge_file_lineage.sh

      - name: Complete the config.yaml with secrets
        run: |
          echo "    server: ${{ secrets.DATAHUB_SINK_SERVER }}" >> ${GITHUB_WORKSPACE}/airplant/data-hub/config/config.yaml
          echo "    token: ${{ secrets.DATAHUB_SINK_TOKEN }}" >> ${GITHUB_WORKSPACE}/airplant/data-hub/config/config.yaml

      - name: Run Datahub Ingest
        run: |
          source venv/bin/activate
          cd ${GITHUB_WORKSPACE}
          datahub ingest -c ./airplant/data-hub/config/config.yaml
