import datetime
import pendulum
from airflow import DAG
from airflow.sensors.time_delta import TimeDeltaSensorAsync
from airflow.operators.bash import BashOperator
from airflow.operators.empty import EmptyOperator

with DAG(
    dag_id="sample_async_1h_wait",
    start_date=datetime.datetime(2025, 4, 1, tzinfo=pendulum.timezone("Asia/Seoul")),
    end_date=datetime.datetime(2025, 5, 1, tzinfo=pendulum.timezone("Asia/Seoul")),
    schedule="@hourly",
):
    start = TimeDeltaSensorAsync(
        task_id="start",
        delta=datetime.timedelta(minutes=15),
        timeout=3600 * 1
    )
    run = BashOperator(task_id="wait", bash_command="sleep 30")
    end = EmptyOperator(task_id="end")
    start >> run >> end
