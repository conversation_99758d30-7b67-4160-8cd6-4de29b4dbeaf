USE addata__db_meta{{params.db_postfix}};

CREATE EXTERNAL TABLE IF NOT EXISTS sales_bi_agency (
  --------------------------- ## 플랫폼 정보 ## ---------------
   dsp                    STRING     COMMENT '[K]DSP 플랫폼 유형'
  --------------------------- ## agency 정보 ## --------------
  ,agency_id              STRING     COMMENT '[K]대행사 ID'
  ,agency_name            STRING     COMMENT '대행사 이름'
  ,business_tax_number    STRING     COMMENT '사업자등록번호'
  ,is_proxy_agency        BOOLEAN    COMMENT '[K]대대행사 여부'
)
COMMENT 'DSP 전체 통합 대행사 테이블'
STORED AS ORC
TBLPROPERTIES (
  "external.table.purge"="true",
  "orc.compress"="ZLIB"
);

LOAD DATA INPATH '{{ params.target_location }}/ymd={{logical_date | basetime | ds}}' OVERWRITE INTO TABLE sales_bi_agency;
