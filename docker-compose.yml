version: "3"

services:

# ssp-batch
  ssp-batch:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      NODE_ENV: development
    command: pm2-runtime start --no-auto-exit pm2.process.config.js
    ports:
      - 12000:3000
      - 3000:3000
    volumes:
        - ./app:/home1/irteam/deploy/app
        - ./config:/home1/irteam/deploy/config
        - ./src/server.js:/home1/irteam/deploy/server.js
		- ./.babelrc:/home1/irteam/deploy/.babelrc
		- ./package.json:/home1/irteam/deploy/package.json
		- ./pm2.process.config.js:/home1/irteam/deploy/pm2.process.config.js
    stop_grace_period: 1h
    networks:
      - shared

# nginx + naver auth module
  nginx:
    image: reg.navercorp.com/gfp/nginx-1.13.8:1.0.2
    links:
      - ssp-batch
    volumes:
      - ./support/nginx/nginx.conf:/home1/irteam/apps/nginx/conf/nginx.conf
      - ./support/nginx/ssl/cert_chain.pem:/home1/irteam/apps/nginx/ssl/cert_chain.pem
      - ./support/nginx/ssl/private.key:/home1/irteam/apps/nginx/ssl/private.key
    ports:
      - 81:80
      - 444:443
    stop_grace_period: 1h
    networks:
      - shared

networks:
  shared:
    driver: bridge
    ipam:
     config:
       - subnet: *********/10
